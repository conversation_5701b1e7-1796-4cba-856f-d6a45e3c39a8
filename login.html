<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Student Management System</title>
    
    <!-- Material Design Components CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="login-styles.css">
</head>
<body>
    <!-- Page Preloader -->
    <div class="page-preloader" id="page-preloader">
        <div class="preloader-content">
            <div class="mdc-circular-progress mdc-circular-progress--large mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading..." aria-valuemin="0" aria-valuemax="1" id="page-preloader-spinner">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="24" cy="24" r="18" stroke-width="4"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="113.097" stroke-width="4"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="3.2"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="preloader-text">Signing in...</div>
        </div>
    </div>

    <!-- Login Container -->
    <div class="login-container">
        <!-- Background Pattern -->
        <div class="background-pattern"></div>
        
        <!-- Login Card -->
        <div class="login-card mdc-card">
            <div class="login-header">
                <div class="school-logo">
                    <span class="material-icons">school</span>
                </div>
                <h1 class="login-title">Student Management System</h1>
                <p class="login-subtitle">Sign in to your account</p>
            </div>

            <form class="login-form" id="login-form">
                <!-- School Year Field -->
                <div class="form-field school-year-field">
                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon">
                        <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">event</span>
                        <select class="mdc-text-field__input" id="school-year" required>
                            <option value="">Select School Year</option>
                            <option value="2024-2025" selected>2024-2025</option>
                            <option value="2023-2024">2023-2024</option>
                            <option value="2022-2023">2022-2023</option>
                        </select>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="school-year" class="mdc-floating-label">School Year</label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>

                <!-- Username Field -->
                <div class="form-field">
                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon">
                        <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">person</span>
                        <input type="text" class="mdc-text-field__input" id="username" required>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="username" class="mdc-floating-label">Username</label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>

                <!-- Password Field -->
                <div class="form-field">
                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon mdc-text-field--with-trailing-icon">
                        <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">lock</span>
                        <input type="password" class="mdc-text-field__input" id="password" required>
                        <button type="button" class="mdc-icon-button mdc-text-field__icon mdc-text-field__icon--trailing" id="toggle-password">
                            <span class="material-icons">visibility</span>
                        </button>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="password" class="mdc-floating-label">Password</label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>

                <!-- Remember Me Checkbox -->
                <div class="form-field checkbox-field">
                    <div class="mdc-form-field">
                        <div class="mdc-checkbox">
                            <input type="checkbox" class="mdc-checkbox__native-control" id="remember-me">
                            <div class="mdc-checkbox__background">
                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                </svg>
                                <div class="mdc-checkbox__mixedmark"></div>
                            </div>
                            <div class="mdc-checkbox__ripple"></div>
                        </div>
                        <label for="remember-me">Remember me</label>
                    </div>
                </div>

                <!-- Login Button -->
                <div class="form-field">
                    <button type="submit" class="mdc-button mdc-button--raised login-button" id="login-button">
                        <span class="mdc-button__ripple"></span>
                        <span class="material-icons mdc-button__icon">login</span>
                        <span class="mdc-button__label">Sign In</span>
                    </button>
                </div>

                <!-- Forgot Password Link -->
                <div class="form-field">
                    <a href="#" class="forgot-password-link">Forgot your password?</a>
                </div>
            </form>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <p>&copy; 2024 Student Management System. All rights reserved.</p>
        </div>
    </div>

    <!-- Material Design Components JS -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>
    
    <!-- Custom JS -->
    <script src="login-script.js"></script>
</body>
</html>
