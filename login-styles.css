/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>', sans-serif;
    background: linear-gradient(135deg, #6200ea, #7c4dff);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow-x: hidden;
}

/* Page Preloader Styles */
.page-preloader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #6200ea, #7c4dff);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.page-preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

.preloader-content {
    text-align: center;
    color: white;
}

.preloader-content .mdc-circular-progress__determinate-track,
.preloader-content .mdc-circular-progress__determinate-circle,
.preloader-content .mdc-circular-progress__indeterminate-circle-graphic circle {
    stroke: white;
}

.preloader-text {
    margin-top: 16px;
    font-size: 16px;
    font-weight: 400;
}

/* Background Pattern */
.background-pattern {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

/* Login Container */
.login-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    padding: 20px;
    z-index: 1;
}

/* Login Card */
.login-card {
    background: white;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Login Header */
.login-header {
    text-align: center;
    margin-bottom: 32px;
}

.school-logo {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #6200ea, #7c4dff);
    border-radius: 50%;
    margin-bottom: 16px;
}

.school-logo .material-icons {
    font-size: 32px;
    color: white;
}

.login-title {
    font-size: 24px;
    font-weight: 500;
    color: #212121;
    margin-bottom: 8px;
}

.login-subtitle {
    font-size: 14px;
    color: #757575;
    font-weight: 400;
}

/* Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-field {
    width: 100%;
}

/* Ensure all form inputs have consistent width */
.mdc-text-field,
.mdc-select {
    width: 100% !important;
}

.mdc-text-field__input {
    width: 100%;
}

.mdc-select__anchor {
    width: 100%;
}

.checkbox-field {
    margin: 8px 0;
}

.mdc-form-field {
    display: flex;
    align-items: center;
    gap: 8px;
}

.mdc-form-field label {
    font-size: 14px;
    color: #424242;
    cursor: pointer;
}

/* Login Button */
.login-button {
    width: 100%;
    height: 48px;
    background: linear-gradient(135deg, #6200ea, #7c4dff) !important;
    color: white !important;
    font-weight: 500;
    text-transform: none;
    font-size: 16px;
    border-radius: 8px;
}

.login-button:hover {
    background: linear-gradient(135deg, #5500d4, #6c3dff) !important;
}

.login-button .mdc-button__icon {
    margin-right: 8px;
}

/* Forgot Password Link */
.forgot-password-link {
    display: block;
    text-align: center;
    color: #6200ea;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.2s ease;
}

.forgot-password-link:hover {
    color: #7c4dff;
    text-decoration: underline;
}

/* Footer */
.login-footer {
    text-align: center;
    margin-top: 24px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
}

/* Material Design Customizations */
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: #e0e0e0;
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__trailing {
    border-color: #6200ea;
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: #6200ea;
    border-width: 2px;
}

.mdc-text-field--focused .mdc-floating-label {
    color: #6200ea;
}

.mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__leading,
.mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__notch,
.mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__trailing {
    border-color: #e0e0e0;
}

.mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__leading,
.mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__notch,
.mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__trailing {
    border-color: #6200ea;
}

.mdc-select--focused .mdc-notched-outline__leading,
.mdc-select--focused .mdc-notched-outline__notch,
.mdc-select--focused .mdc-notched-outline__trailing {
    border-color: #6200ea;
    border-width: 2px;
}

.mdc-select--focused .mdc-floating-label {
    color: #6200ea;
}

/* Fix dropdown z-index to appear above other elements */
.mdc-select__menu {
    z-index: 9999;
}

.mdc-menu-surface {
    z-index: 9999;
}

.mdc-select__menu .mdc-menu-surface {
    z-index: 9999;
}

.mdc-select {
    z-index: 100;
}

.mdc-select--focused {
    z-index: 200;
}

/* Ensure select field renders properly and in correct position */
.mdc-select {
    display: block !important;
    position: relative;
    visibility: visible !important;
    opacity: 1 !important;
}

.mdc-select__anchor {
    display: flex !important;
    position: relative;
    visibility: visible !important;
}

/* Force the first form field (school year) to be visible and positioned correctly */
.form-field:first-child {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
}

/* Checkbox Customization */
.mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
    background-color: #6200ea;
    border-color: #6200ea;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        padding: 16px;
    }
    
    .login-card {
        padding: 24px;
    }
    
    .login-title {
        font-size: 20px;
    }
    
    .school-logo {
        width: 56px;
        height: 56px;
    }
    
    .school-logo .material-icons {
        font-size: 28px;
    }
}

/* Ensure proper form field ordering */
.form-field {
    order: initial;
}

/* Explicitly set order for each form field */
.form-field:nth-child(1) { order: 1; }  /* School Year */
.form-field:nth-child(2) { order: 2; }  /* Username */
.form-field:nth-child(3) { order: 3; }  /* Password */
.form-field:nth-child(4) { order: 4; }  /* Remember Me */
.form-field:nth-child(5) { order: 5; }  /* Login Button */
.form-field:nth-child(6) { order: 6; }  /* Forgot Password */

/* Animation for form fields */
.form-field {
    animation: slideInUp 0.3s ease forwards;
    opacity: 0;
    transform: translateY(20px);
    animation-fill-mode: forwards;
}

.form-field:nth-child(1) { animation-delay: 0.1s; }
.form-field:nth-child(2) { animation-delay: 0.2s; }
.form-field:nth-child(3) { animation-delay: 0.3s; }
.form-field:nth-child(4) { animation-delay: 0.4s; }
.form-field:nth-child(5) { animation-delay: 0.5s; }
.form-field:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
