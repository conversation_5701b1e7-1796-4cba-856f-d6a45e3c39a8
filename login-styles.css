/* ========================================
   CSS CUSTOM PROPERTIES (COLOR PALETTE)
   ======================================== */
:root {
    /* Primary Colors */
    --primary-color: #1976d2;
    --primary-light: #42a5f5;
    --primary-dark: #1565c0;
    --primary-gradient: linear-gradient(135deg, var(--primary-color), var(--primary-light));

    /* Secondary Colors */
    --secondary-color: #03dac6;
    --secondary-light: #66fff9;
    --secondary-dark: #00a896;

    /* Background Colors */
    --background-primary: #f5f5f5;
    --background-secondary: #fafafa;
    --background-surface: #ffffff;
    --background-overlay: rgba(255, 255, 255, 0.9);

    /* Text Colors */
    --text-primary: #212121;
    --text-secondary: #757575;
    --text-disabled: #9e9e9e;
    --text-on-primary: #ffffff;
    --text-on-surface: #212121;

    /* Border Colors */
    --border-light: #e0e0e0;
    --border-medium: #bdbdbd;
    --border-dark: #9e9e9e;

    /* State Colors */
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;

    /* Surface Colors */
    --surface-hover: #f5f5f5;
    --surface-selected: #e3f2fd;
    --surface-disabled: #f5f5f5;

    /* Shadow Colors */
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.2);
    --shadow-dark: rgba(0, 0, 0, 0.3);

    /* Opacity Values */
    --opacity-disabled: 0.6;
    --opacity-hover: 0.8;
    --opacity-pressed: 0.12;

    /* Primary Color Variants with Opacity */
    --primary-alpha-8: rgba(25, 118, 210, 0.08);
    --primary-alpha-12: rgba(25, 118, 210, 0.12);
    --primary-alpha-16: rgba(25, 118, 210, 0.16);
    --primary-alpha-24: rgba(25, 118, 210, 0.24);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: var(--background-primary);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow-x: hidden;
}

/* Page Preloader Styles */
.page-preloader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-primary);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.page-preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

.preloader-content {
    text-align: center;
    color: var(--primary-color);
}

.preloader-content .mdc-circular-progress__determinate-track,
.preloader-content .mdc-circular-progress__determinate-circle,
.preloader-content .mdc-circular-progress__indeterminate-circle-graphic circle {
    stroke: var(--primary-color);
}

.preloader-text {
    margin-top: 16px;
    font-size: 16px;
    font-weight: 400;
}

/* Background Pattern */
.background-pattern {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(98, 0, 234, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(98, 0, 234, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(98, 0, 234, 0.02) 0%, transparent 50%);
    pointer-events: none;
}

/* Login Container */
.login-container {
    position: relative;
    width: 100%;
    max-width: 1200px;
    padding: 20px;
    z-index: 1;
}

/* Login Card */
.login-card {
    background: var(--background-surface);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 20px 60px var(--shadow-light);
    border: 1px solid var(--border-light);
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 600px;
}

/* Left Column - App Information */
.login-info-section {
    background: var(--primary-gradient);
    color: var(--text-on-primary);
    padding: 60px 48px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-info-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.08)"/><circle cx="10" cy="60" r="0.8" fill="rgba(255,255,255,0.06)"/><circle cx="90" cy="30" r="0.6" fill="rgba(255,255,255,0.04)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.info-content {
    position: relative;
    z-index: 1;
}

.app-branding {
    margin-bottom: 48px;
}

.brand-logo {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    backdrop-filter: blur(10px);
}

.brand-logo .material-icons {
    font-size: 40px;
    color: var(--text-on-primary);
}

.brand-title {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.2;
}

.brand-subtitle {
    font-size: 18px;
    opacity: 0.9;
    font-weight: 300;
}

/* Feature Highlights */
.feature-highlights {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

.feature-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon .material-icons {
    font-size: 24px;
    color: var(--text-on-primary);
}

.feature-text h3 {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
}

.feature-text p {
    font-size: 14px;
    opacity: 0.8;
    line-height: 1.4;
}

/* Right Column - Login Form */
.login-form-section {
    padding: 60px 48px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.form-content {
    max-width: 400px;
    margin: 0 auto;
    width: 100%;
}

.form-header {
    text-align: center;
    margin-bottom: 40px;
}

.form-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.form-subtitle {
    font-size: 16px;
    color: var(--text-secondary);
    font-weight: 400;
}

/* Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-field {
    width: 100%;
}

/* Ensure all form inputs have consistent width */
.mdc-text-field,
.mdc-select {
    width: 100% !important;
}

.mdc-text-field__input {
    width: 100%;
}

.mdc-select__anchor {
    width: 100%;
}

.checkbox-field {
    margin: 8px 0;
}

.mdc-form-field {
    display: flex;
    align-items: center;
    gap: 8px;
}

.mdc-form-field label {
    font-size: 14px;
    color: var(--text-secondary);
    cursor: pointer;
}

/* Login Button */
.login-button {
    width: 100%;
    height: 52px;
    background: var(--primary-gradient) !important;
    color: var(--text-on-primary) !important;
    font-weight: 500;
    text-transform: none;
    font-size: 16px;
    border-radius: 12px;
    box-shadow: 0 4px 12px var(--primary-alpha-24);
    transition: all 0.3s ease;
}

.login-button:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-light)) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--primary-alpha-24);
}

.login-button .mdc-button__icon {
    margin-right: 8px;
}

/* Forgot Password Link */
.forgot-password-link {
    display: block;
    text-align: center;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.2s ease;
}

.forgot-password-link:hover {
    color: var(--primary-light);
    text-decoration: underline;
}

/* Form Footer */
.form-footer {
    text-align: center;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid var(--border-light);
    color: var(--text-disabled);
    font-size: 12px;
}

/* Material Design Customizations */
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: var(--border-light);
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
    border-width: 2px;
}

.mdc-text-field--focused .mdc-floating-label {
    color: var(--primary-color);
}

.mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__leading,
.mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__notch,
.mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__trailing {
    border-color: var(--border-light);
}

.mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__leading,
.mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__notch,
.mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
}

.mdc-select--focused .mdc-notched-outline__leading,
.mdc-select--focused .mdc-notched-outline__notch,
.mdc-select--focused .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
    border-width: 2px;
}

.mdc-select--focused .mdc-floating-label {
    color: var(--primary-color);
}

/* Fix dropdown z-index to appear above other elements */
.mdc-select__menu {
    z-index: 9999;
}

.mdc-menu-surface {
    z-index: 9999;
}

.mdc-select__menu .mdc-menu-surface {
    z-index: 9999;
}

.mdc-select {
    z-index: 100;
}

.mdc-select--focused {
    z-index: 200;
}

/* Ensure select field renders properly and in correct position */
.mdc-select {
    display: block !important;
    position: relative;
    visibility: visible !important;
    opacity: 1 !important;
}

.mdc-select__anchor {
    display: flex !important;
    position: relative;
    visibility: visible !important;
}

/* Force the first form field (school year) to be visible and positioned correctly */
.form-field:first-child {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
}

/* Checkbox Customization */
.mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .login-container {
        max-width: 900px;
    }

    .login-info-section,
    .login-form-section {
        padding: 48px 32px;
    }

    .brand-title {
        font-size: 28px;
    }

    .form-title {
        font-size: 24px;
    }
}

@media (max-width: 768px) {
    .login-card {
        grid-template-columns: 1fr;
        min-height: auto;
    }

    .login-info-section {
        padding: 40px 32px;
        text-align: center;
    }

    .login-form-section {
        padding: 40px 32px;
    }

    .feature-highlights {
        flex-direction: row;
        justify-content: space-around;
        gap: 16px;
    }

    .feature-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
        flex: 1;
    }

    .feature-text h3 {
        font-size: 16px;
    }

    .feature-text p {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 16px;
    }

    .login-info-section,
    .login-form-section {
        padding: 32px 24px;
    }

    .brand-title {
        font-size: 24px;
    }

    .form-title {
        font-size: 22px;
    }

    .brand-logo {
        width: 64px;
        height: 64px;
    }

    .brand-logo .material-icons {
        font-size: 32px;
    }

    .feature-highlights {
        display: none;
    }
}

/* Ensure proper form field ordering */
.form-field {
    order: initial;
}

/* Explicitly set order for each form field */
.form-field:nth-child(1) { order: 1; }  /* School Year */
.form-field:nth-child(2) { order: 2; }  /* Username */
.form-field:nth-child(3) { order: 3; }  /* Password */
.form-field:nth-child(4) { order: 4; }  /* Remember Me */
.form-field:nth-child(5) { order: 5; }  /* Login Button */
.form-field:nth-child(6) { order: 6; }  /* Forgot Password */

/* Animation for form fields */
.form-field {
    animation: slideInUp 0.3s ease forwards;
    opacity: 0;
    transform: translateY(20px);
    animation-fill-mode: forwards;
}

.form-field:nth-child(1) { animation-delay: 0.1s; }
.form-field:nth-child(2) { animation-delay: 0.2s; }
.form-field:nth-child(3) { animation-delay: 0.3s; }
.form-field:nth-child(4) { animation-delay: 0.4s; }
.form-field:nth-child(5) { animation-delay: 0.5s; }
.form-field:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
