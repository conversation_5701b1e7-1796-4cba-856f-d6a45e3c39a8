/* ========================================
   CSS CUSTOM PROPERTIES (COLOR PALETTE)
   ======================================== */
:root {
    /* Primary Colors */
    --primary-color: #1976d2;
    --primary-light: #42a5f5;
    --primary-dark: #1565c0;
    --primary-gradient: linear-gradient(135deg, var(--primary-color), var(--primary-light));

    /* Secondary Colors */
    --secondary-color: #03dac6;
    --secondary-light: #66fff9;
    --secondary-dark: #00a896;

    /* Background Colors */
    --background-primary: #f5f5f5;
    --background-secondary: #fafafa;
    --background-surface: #ffffff;
    --background-overlay: rgba(255, 255, 255, 0.9);

    /* Text Colors */
    --text-primary: #212121;
    --text-secondary: #757575;
    --text-disabled: #9e9e9e;
    --text-on-primary: #ffffff;
    --text-on-surface: #212121;

    /* Border Colors */
    --border-light: #e0e0e0;
    --border-medium: #bdbdbd;
    --border-dark: #9e9e9e;

    /* State Colors */
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;

    /* Surface Colors */
    --surface-hover: #f5f5f5;
    --surface-selected: #e3f2fd;
    --surface-disabled: #f5f5f5;

    /* Shadow Colors */
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.2);
    --shadow-dark: rgba(0, 0, 0, 0.3);

    /* Opacity Values */
    --opacity-disabled: 0.6;
    --opacity-hover: 0.8;
    --opacity-pressed: 0.12;

    /* Primary Color Variants with Opacity */
    --primary-alpha-8: rgba(25, 118, 210, 0.08);
    --primary-alpha-12: rgba(25, 118, 210, 0.12);
    --primary-alpha-16: rgba(25, 118, 210, 0.16);
    --primary-alpha-24: rgba(25, 118, 210, 0.24);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: var(--background-primary);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow-x: hidden;
}

/* Page Preloader Styles */
.page-preloader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-primary);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.page-preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

.preloader-content {
    text-align: center;
    color: var(--primary-color);
}

.preloader-content .mdc-circular-progress__determinate-track,
.preloader-content .mdc-circular-progress__determinate-circle,
.preloader-content .mdc-circular-progress__indeterminate-circle-graphic circle {
    stroke: var(--primary-color);
}

.preloader-text {
    margin-top: 16px;
    font-size: 16px;
    font-weight: 400;
}

/* Background Pattern */
.background-pattern {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(98, 0, 234, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(98, 0, 234, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(98, 0, 234, 0.02) 0%, transparent 50%);
    pointer-events: none;
}

/* Login Container */
.login-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    padding: 20px;
    z-index: 1;
}

/* Login Card */
.login-card {
    background: var(--background-surface);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 4px 20px var(--shadow-light);
    border: 1px solid var(--border-light);
}

/* Login Header */
.login-header {
    text-align: center;
    margin-bottom: 32px;
}



.school-logo {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    background: var(--primary-gradient);
    border-radius: 50%;
    margin-bottom: 16px;
}

.school-logo .material-icons {
    font-size: 32px;
    color: var(--text-on-primary);
}

.login-title {
    font-size: 24px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.login-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 400;
}

/* Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-field {
    width: 100%;
}

/* Ensure all form inputs have consistent width */
.mdc-text-field,
.mdc-select {
    width: 100% !important;
}

.mdc-text-field__input {
    width: 100%;
}

.mdc-select__anchor {
    width: 100%;
}

.checkbox-field {
    margin: 8px 0;
}

.mdc-form-field {
    display: flex;
    align-items: center;
    gap: 8px;
}

.mdc-form-field label {
    font-size: 14px;
    color: var(--text-secondary);
    cursor: pointer;
}

/* Login Button */
.login-button {
    width: 100%;
    height: 48px;
    background: var(--primary-gradient) !important;
    color: var(--text-on-primary) !important;
    font-weight: 500;
    text-transform: none;
    font-size: 16px;
    border-radius: 8px;
}

.login-button:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-light)) !important;
}

.login-button .mdc-button__icon {
    margin-right: 8px;
}

/* Forgot Password Link */
.forgot-password-link {
    display: block;
    text-align: center;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.2s ease;
}

.forgot-password-link:hover {
    color: var(--primary-light);
    text-decoration: underline;
}

/* Footer */
.login-footer {
    text-align: center;
    margin-top: 24px;
    color: var(--text-disabled);
    font-size: 12px;
}

/* Material Design Customizations */
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: var(--border-light);
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
}

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
    border-width: 2px;
}

.mdc-text-field--focused .mdc-floating-label {
    color: var(--primary-color);
}

.mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__leading,
.mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__notch,
.mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__trailing {
    border-color: var(--border-light);
}

.mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__leading,
.mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__notch,
.mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
}

.mdc-select--focused .mdc-notched-outline__leading,
.mdc-select--focused .mdc-notched-outline__notch,
.mdc-select--focused .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
    border-width: 2px;
}

.mdc-select--focused .mdc-floating-label {
    color: var(--primary-color);
}

/* Fix dropdown z-index to appear above other elements */
.mdc-select__menu {
    z-index: 9999;
}

.mdc-menu-surface {
    z-index: 9999;
}

.mdc-select__menu .mdc-menu-surface {
    z-index: 9999;
}

.mdc-select {
    z-index: 100;
}

.mdc-select--focused {
    z-index: 200;
}

/* Ensure select field renders properly and in correct position */
.mdc-select {
    display: block !important;
    position: relative;
    visibility: visible !important;
    opacity: 1 !important;
}

.mdc-select__anchor {
    display: flex !important;
    position: relative;
    visibility: visible !important;
}

/* Force the first form field (school year) to be visible and positioned correctly */
.form-field:first-child {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
}

/* Checkbox Customization */
.mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        padding: 16px;
    }
    
    .login-card {
        padding: 24px;
    }
    
    .login-title {
        font-size: 20px;
    }
    
    .school-logo {
        width: 56px;
        height: 56px;
    }
    
    .school-logo .material-icons {
        font-size: 28px;
    }
}

/* Ensure proper form field ordering */
.form-field {
    order: initial;
}

/* Explicitly set order for each form field */
.form-field:nth-child(1) { order: 1; }  /* School Year */
.form-field:nth-child(2) { order: 2; }  /* Username */
.form-field:nth-child(3) { order: 3; }  /* Password */
.form-field:nth-child(4) { order: 4; }  /* Remember Me */
.form-field:nth-child(5) { order: 5; }  /* Login Button */
.form-field:nth-child(6) { order: 6; }  /* Forgot Password */

/* Animation for form fields */
.form-field {
    animation: slideInUp 0.3s ease forwards;
    opacity: 0;
    transform: translateY(20px);
    animation-fill-mode: forwards;
}

.form-field:nth-child(1) { animation-delay: 0.1s; }
.form-field:nth-child(2) { animation-delay: 0.2s; }
.form-field:nth-child(3) { animation-delay: 0.3s; }
.form-field:nth-child(4) { animation-delay: 0.4s; }
.form-field:nth-child(5) { animation-delay: 0.5s; }
.form-field:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
