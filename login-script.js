// Initialize Material Design Components
mdc.autoInit();

// Initialize page preloader spinner
let pagePreloaderSpinner;
if (window.mdc && window.mdc.circularProgress) {
    const preloaderElement = document.getElementById('page-preloader-spinner');
    if (preloaderElement) {
        pagePreloaderSpinner = new mdc.circularProgress.MDCCircularProgress(preloaderElement);
    }
}

// Loading Functions
function showPagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.remove('hidden');
    
    // Start the spinner animation
    if (pagePreloaderSpinner) {
        pagePreloaderSpinner.open();
    }
}

function hidePagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.add('hidden');
    
    // Stop the spinner animation
    if (pagePreloaderSpinner) {
        pagePreloaderSpinner.close();
    }

    // Remove preloader from DOM after animation completes
    setTimeout(() => {
        preloader.style.display = 'none';
    }, 500);
}

// Initialize components when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Show preloader initially
    showPagePreloader();

    // Initialize Material Design Components
    let schoolYearSelect, usernameField, passwordField, rememberMeCheckbox, loginButton;

    // Initialize School Year Text Field (with select input)
    const schoolYearFieldEl = document.querySelector('#school-year').closest('.mdc-text-field');
    if (schoolYearFieldEl && window.mdc && window.mdc.textField) {
        schoolYearSelect = new mdc.textField.MDCTextField(schoolYearFieldEl);
    }

    // Initialize Username Text Field
    const usernameFieldEl = document.querySelector('#username').closest('.mdc-text-field');
    if (usernameFieldEl && window.mdc && window.mdc.textField) {
        usernameField = new mdc.textField.MDCTextField(usernameFieldEl);
    }

    // Initialize Password Text Field
    const passwordFieldEl = document.querySelector('#password').closest('.mdc-text-field');
    if (passwordFieldEl && window.mdc && window.mdc.textField) {
        passwordField = new mdc.textField.MDCTextField(passwordFieldEl);
    }

    // Initialize Remember Me Checkbox
    const rememberMeCheckboxEl = document.querySelector('.mdc-checkbox');
    if (rememberMeCheckboxEl && window.mdc && window.mdc.checkbox) {
        rememberMeCheckbox = new mdc.checkbox.MDCCheckbox(rememberMeCheckboxEl);
    }

    // Initialize Login Button
    const loginButtonEl = document.getElementById('login-button');
    if (loginButtonEl && window.mdc && window.mdc.ripple) {
        loginButton = new mdc.ripple.MDCRipple(loginButtonEl);
    }

    // Initialize Password Toggle Button
    const togglePasswordBtn = document.getElementById('toggle-password');
    if (togglePasswordBtn && window.mdc && window.mdc.ripple) {
        new mdc.ripple.MDCRipple(togglePasswordBtn);
    }

    // Password visibility toggle functionality
    const passwordInput = document.getElementById('password');
    const togglePasswordIcon = togglePasswordBtn.querySelector('.material-icons');

    togglePasswordBtn.addEventListener('click', () => {
        const isPassword = passwordInput.type === 'password';
        passwordInput.type = isPassword ? 'text' : 'password';
        togglePasswordIcon.textContent = isPassword ? 'visibility_off' : 'visibility';
    });

    // Form submission handling
    const loginForm = document.getElementById('login-form');
    loginForm.addEventListener('submit', (e) => {
        e.preventDefault();
        
        // Get form values
        const schoolYear = document.getElementById('school-year').value;
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember-me').checked;

        // Basic validation
        if (!schoolYear) {
            alert('Please select a school year');
            return;
        }

        if (!username.trim()) {
            alert('Please enter your username');
            document.getElementById('username').focus();
            return;
        }

        if (!password.trim()) {
            alert('Please enter your password');
            document.getElementById('password').focus();
            return;
        }

        // Show loading state
        loginButton.disabled = true;
        const buttonLabel = loginButtonEl.querySelector('.mdc-button__label');
        const originalText = buttonLabel.textContent;
        buttonLabel.textContent = 'Signing in...';

        // Simulate login process
        setTimeout(() => {
            // Here you would typically make an API call to authenticate
            console.log('Login attempt:', {
                schoolYear,
                username,
                password,
                rememberMe
            });

            // For demo purposes, simulate successful login
            if (username === 'admin' && password === 'password') {
                // Show success message
                showPagePreloader();
                
                // Redirect to main page after a delay
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            } else {
                // Show error message
                alert('Invalid username or password. Try admin/password for demo.');
                
                // Reset button state
                loginButton.disabled = false;
                buttonLabel.textContent = originalText;
                
                // Clear password field
                passwordInput.value = '';
                passwordInput.focus();
            }
        }, 1000);
    });

    // Auto-focus username field after page loads
    setTimeout(() => {
        document.getElementById('username').focus();
    }, 100);

    // Hide preloader after components are initialized
    setTimeout(() => {
        hidePagePreloader();
    }, 800);
});

// Handle form field animations
window.addEventListener('load', () => {
    // Trigger form field animations
    const formFields = document.querySelectorAll('.form-field');
    formFields.forEach((field, index) => {
        setTimeout(() => {
            field.style.animationPlayState = 'running';
        }, index * 100);
    });
});

// Handle Enter key in form fields
document.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
        const activeElement = document.activeElement;
        
        if (activeElement.id === 'username') {
            document.getElementById('password').focus();
        } else if (activeElement.id === 'password') {
            document.getElementById('login-form').dispatchEvent(new Event('submit'));
        }
    }
});

// Add ripple effect to forgot password link
document.addEventListener('DOMContentLoaded', () => {
    const forgotPasswordLink = document.querySelector('.forgot-password-link');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', (e) => {
            e.preventDefault();
            alert('Password reset functionality would be implemented here.');
        });
    }
});
