<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students - Management System</title>
    
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Page Preloader -->
    <div class="page-preloader" id="page-preloader">
        <div class="preloader-content">
            <div class="mdc-circular-progress mdc-circular-progress--large mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading..." aria-valuemin="0" aria-valuemax="1" id="page-preloader-spinner">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="24" cy="24" r="18" stroke-width="4"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="113.097" stroke-width="4"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="3.2"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="preloader-text">Loading...</div>
        </div>
    </div>

    <!-- Loading Overlay for UI Interactions -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="mdc-circular-progress mdc-circular-progress--medium" role="progressbar" aria-label="Processing..." aria-valuemin="0" aria-valuemax="1">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="16" cy="16" r="12.5" stroke-width="3"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="78.54" stroke-width="3"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="2.4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="loading-text" id="loading-text">Processing...</div>
        </div>
    </div>

    <!-- App Bar -->
    <div class="app-bar">
        <span class="material-icons" id="menu-btn">menu</span>
        <h1>Students</h1>
        <div class="actions">
            <span class="material-icons">search</span>
            <span class="material-icons" id="more-menu-btn">more_vert</span>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-section">
            <a href="#" class="sidebar-item">
                <span class="material-icons">home</span>
                <span>Home</span>
            </a>
            <a href="#" class="sidebar-item active">
                <span class="material-icons">people</span>
                <span>Students</span>
            </a>
            <div class="sidebar-item" id="grades-menu">
                <span class="material-icons">grade</span>
                <span>Grades</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="grades-submenu">
                <a href="#" class="submenu-item">Grades List</a>
                <a href="#" class="submenu-item">Subjects</a>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="content-header">
            <span class="material-icons">arrow_back</span>
            <h2 class="page-title">Students List</h2>
            <div class="actions">
                <button class="mdc-button mdc-button--raised add-student-btn" id="add-student-btn">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons mdc-button__icon">add</span>
                    <span class="mdc-button__label">Add Student</span>
                </button>
                <span class="material-icons" title="Export to Excel">description</span>
                <span class="material-icons" title="Import Data">file_upload</span>
                <span class="material-icons" title="Toggle View">view_module</span>
                <span class="material-icons" title="Filter" id="filter-btn">filter_list</span>
            </div>
        </div>

        <!-- Filter Chips Container -->
        <div class="filter-chips-container" id="filter-chips-container">
            <!-- Filter chips will be dynamically added here -->
        </div>

        <!-- Data Table (Desktop) -->
        <div class="data-table-container">
            <!-- Targeted Loading Overlay for Table -->
            <div class="target-loading-overlay" id="table-loading-overlay">
                <div class="target-loading-content">
                    <div class="simple-spinner">
                        <svg class="spinner-svg" viewBox="0 0 50 50">
                            <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#6200ea" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                    </div>
                    <div class="target-loading-text" id="table-loading-text">Loading...</div>
                </div>
            </div>

            <!-- Table Controls -->
            <div class="table-controls">
                <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon mdc-small-input">
                    <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                    <input type="text" class="mdc-text-field__input" id="table-search" placeholder="Search students...">
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__notch">
                            <label for="table-search" class="mdc-floating-label">Search students</label>
                        </div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>
                <div class="per-page-container">
                    <span class="per-page-label">Rows per page:</span>
                    <div class="mdc-select mdc-select--outlined mdc-small-input">
                        <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">10</span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list" role="listbox">
                                <li class="mdc-deprecated-list-item" data-value="5" role="option">
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">5</span>
                                </li>
                                <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="10" role="option" aria-selected="true">
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">10</span>
                                </li>
                                <li class="mdc-deprecated-list-item" data-value="25" role="option">
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">25</span>
                                </li>
                                <li class="mdc-deprecated-list-item" data-value="50" role="option">
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">50</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- MDC Data Table -->
            <div class="mdc-data-table">
                <div class="mdc-data-table__table-container">
                    <table class="mdc-data-table__table" aria-label="Students list">
                        <thead>
                            <tr class="mdc-data-table__header-row">
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                    <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                        <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-checkbox" aria-label="Toggle all rows selected">
                                        <div class="mdc-checkbox__background">
                                            <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                            </svg>
                                            <div class="mdc-checkbox__mixedmark"></div>
                                        </div>
                                        <div class="mdc-checkbox__ripple"></div>
                                    </div>
                                </th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Photo</th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable" role="columnheader" scope="col" data-column="name">
                                    Name
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable" role="columnheader" scope="col" data-column="level">
                                    Grade
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable" role="columnheader" scope="col" data-column="birth">
                                    Birth Date
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable" role="columnheader" scope="col" data-column="id">
                                    Student ID
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable" role="columnheader" scope="col" data-column="date">
                                    Date Added
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="mdc-data-table__content" id="table-body">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <div class="table-pagination">
                <div class="pagination-info" id="pagination-info">
                    Showing 1-10 of 8 students
                </div>
                <div class="pagination-controls">
                    <button class="mdc-icon-button" id="prev-page" disabled>
                        <span class="material-icons">chevron_left</span>
                    </button>
                    <div class="pagination-pages" id="pagination-pages">
                        <button class="pagination-page active">1</button>
                    </div>
                    <button class="mdc-icon-button" id="next-page" disabled>
                        <span class="material-icons">chevron_right</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Students List (Mobile) -->
        <div class="students-list">
            <!-- Targeted Loading Overlay for Mobile List -->
            <div class="target-loading-overlay" id="list-loading-overlay">
                <div class="target-loading-content">
                    <div class="simple-spinner">
                        <svg class="spinner-svg" viewBox="0 0 50 50">
                            <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#6200ea" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                    </div>
                    <div class="target-loading-text" id="list-loading-text">Loading...</div>
                </div>
            </div>
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Alexander Thompson</div>
                    <div class="student-details">
                        <span class="student-level">Grade 10</span>
                        Born: March 15, 2008 • Student ID: ST2024001
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
            </div>

            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Sophia Rodriguez</div>
                    <div class="student-details">
                        <span class="student-level">Grade 11</span>
                        Born: July 22, 2007 • Student ID: ST2024002
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
            </div>

            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Marcus Johnson</div>
                    <div class="student-details">
                        <span class="student-level">Grade 9</span>
                        Born: November 8, 2009 • Student ID: ST2024003
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
            </div>

            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Emma Williams</div>
                    <div class="student-details">
                        <span class="student-level">Grade 12</span>
                        Born: February 3, 2006 • Student ID: ST2024004
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Daniel Chen</div>
                    <div class="student-details">
                        <span class="student-level">Grade 10</span>
                        Born: September 12, 2008 • Student ID: ST2024005
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
            </div>

            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Isabella Garcia</div>
                    <div class="student-details">
                        <span class="student-level">Grade 11</span>
                        Born: May 18, 2007 • Student ID: ST2024006
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
            </div>

            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Ryan Martinez</div>
                    <div class="student-details">
                        <span class="student-level">Grade 9</span>
                        Born: December 1, 2009 • Student ID: ST2024007
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
            </div>

            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Olivia Davis</div>
                    <div class="student-details">
                        <span class="student-level">Grade 12</span>
                        Born: April 7, 2006 • Student ID: ST2024008
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button class="mdc-fab mdc-fab--extended" id="add-student-fab">
        <div class="mdc-fab__ripple"></div>
        <span class="material-icons mdc-fab__icon">add</span>
        <span class="mdc-fab__label">Add Student</span>
    </button>

    <!-- Bottom Sheet -->
    <div class="bottom-sheet-overlay" id="bottom-sheet-overlay">
        <div class="bottom-sheet" id="bottom-sheet">
            <div class="bottom-sheet-handle"></div>
            <div class="bottom-sheet-header">
                <div class="bottom-sheet-title">Student Options</div>
                <button class="bottom-sheet-close" id="bottom-sheet-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="bottom-sheet-content">
                <div class="student-preview" id="student-preview">
                    <div class="student-photo" id="preview-photo"></div>
                    <div class="student-info">
                        <div class="student-name" id="preview-name"></div>
                        <div class="student-details" id="preview-details"></div>
                    </div>
                </div>

                <div class="bottom-sheet-actions">
                    <div class="bottom-sheet-action" id="action-details">
                        <span class="material-icons">person</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">View Details</div>
                            <div class="bottom-sheet-action-subtitle">See complete student information</div>
                        </div>
                    </div>

                    <div class="bottom-sheet-action" id="action-edit">
                        <span class="material-icons">edit</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Edit Student</div>
                            <div class="bottom-sheet-action-subtitle">Update student information</div>
                        </div>
                    </div>

                    <div class="bottom-sheet-action" id="action-payment">
                        <span class="material-icons">payment</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Add Payment Record</div>
                            <div class="bottom-sheet-action-subtitle">Record a new payment</div>
                        </div>
                    </div>

                    <div class="bottom-sheet-action delete" id="action-delete">
                        <span class="material-icons">delete</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Delete Student</div>
                            <div class="bottom-sheet-action-subtitle">Remove student from system</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Offcanvas Component -->
    <div class="offcanvas-overlay" id="offcanvas-overlay">
        <div class="offcanvas" id="offcanvas">
            <div class="offcanvas-header">
                <div class="offcanvas-title">User Options</div>
                <button class="offcanvas-close" id="offcanvas-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="offcanvas-content">
                <!-- User Profile Section -->
                <div class="offcanvas-profile">
                    <div class="profile-avatar">
                        <span class="material-icons">account_circle</span>
                    </div>
                    <div class="profile-info">
                        <div class="profile-name">John Doe</div>
                        <div class="profile-email"><EMAIL></div>
                    </div>
                </div>

                <!-- Menu Options -->
                <div class="offcanvas-menu">
                    <div class="offcanvas-menu-item" id="menu-profile">
                        <span class="material-icons">person</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Edit Profile</div>
                            <div class="menu-item-subtitle">Update your personal information</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>

                    <div class="offcanvas-menu-item" id="menu-settings">
                        <span class="material-icons">settings</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Settings</div>
                            <div class="menu-item-subtitle">App preferences and configuration</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>

                    <div class="offcanvas-menu-item" id="menu-notifications">
                        <span class="material-icons">notifications</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Notifications</div>
                            <div class="menu-item-subtitle">Manage notification preferences</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>

                    <div class="offcanvas-menu-item" id="menu-help">
                        <span class="material-icons">help</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Help & Support</div>
                            <div class="menu-item-subtitle">Get help and contact support</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>

                    <div class="offcanvas-divider"></div>

                    <div class="offcanvas-menu-item logout" id="menu-logout">
                        <span class="material-icons">logout</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Logout</div>
                            <div class="menu-item-subtitle">Sign out of your account</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Offcanvas Component -->
    <div class="offcanvas-overlay" id="filter-offcanvas-overlay">
        <div class="offcanvas" id="filter-offcanvas">
            <div class="offcanvas-header">
                <div class="offcanvas-title">Filter Students</div>
                <button class="offcanvas-close" id="filter-offcanvas-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="offcanvas-content">
                <!-- Filter Form -->
                <div class="filter-form">
                    <!-- Grade Filter -->
                    <div class="filter-group">
                        <label class="filter-label">Grade</label>
                        <div class="mdc-select mdc-select--outlined filter-select" id="grade-filter-select">
                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                                <span class="mdc-select__selected-text-container">
                                    <span class="mdc-select__selected-text">All Grades</span>
                                </span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <div class="mdc-notched-outline">
                                    <div class="mdc-notched-outline__leading"></div>
                                    <div class="mdc-notched-outline__trailing"></div>
                                </div>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-deprecated-list" role="listbox">
                                    <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="" role="option" aria-selected="true">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">All Grades</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Grade 9" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Grade 9</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Grade 10" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Grade 10</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Grade 11" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Grade 11</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Grade 12" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Grade 12</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Gender Filter -->
                    <div class="filter-group">
                        <label class="filter-label">Gender</label>
                        <div class="mdc-select mdc-select--outlined filter-select" id="gender-filter-select">
                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                                <span class="mdc-select__selected-text-container">
                                    <span class="mdc-select__selected-text">All Genders</span>
                                </span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <div class="mdc-notched-outline">
                                    <div class="mdc-notched-outline__leading"></div>
                                    <div class="mdc-notched-outline__trailing"></div>
                                </div>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-deprecated-list" role="listbox">
                                    <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="" role="option" aria-selected="true">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">All Genders</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Male" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Male</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Female" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Female</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Birth Year Filter -->
                    <div class="filter-group">
                        <label class="filter-label">Birth Year</label>
                        <div class="mdc-select mdc-select--outlined filter-select" id="birth-year-filter-select">
                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                                <span class="mdc-select__selected-text-container">
                                    <span class="mdc-select__selected-text">All Years</span>
                                </span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <div class="mdc-notched-outline">
                                    <div class="mdc-notched-outline__leading"></div>
                                    <div class="mdc-notched-outline__trailing"></div>
                                </div>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-deprecated-list" role="listbox">
                                    <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="" role="option" aria-selected="true">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">All Years</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="2006" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">2006</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="2007" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">2007</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="2008" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">2008</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="2009" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">2009</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Date Added Filter -->
                    <div class="filter-group">
                        <label class="filter-label">Date Added</label>
                        <div class="date-filter-container">
                            <div class="mdc-text-field mdc-text-field--outlined filter-date-input">
                                <input type="text" class="mdc-text-field__input" id="date-from-filter" placeholder="From date" readonly>
                                <div class="mdc-notched-outline">
                                    <div class="mdc-notched-outline__leading"></div>
                                    <div class="mdc-notched-outline__notch">
                                        <label for="date-from-filter" class="mdc-floating-label">From Date</label>
                                    </div>
                                    <div class="mdc-notched-outline__trailing"></div>
                                </div>
                            </div>
                            <div class="date-separator">to</div>
                            <div class="mdc-text-field mdc-text-field--outlined filter-date-input">
                                <input type="text" class="mdc-text-field__input" id="date-to-filter" placeholder="To date" readonly>
                                <div class="mdc-notched-outline">
                                    <div class="mdc-notched-outline__leading"></div>
                                    <div class="mdc-notched-outline__notch">
                                        <label for="date-to-filter" class="mdc-floating-label">To Date</label>
                                    </div>
                                    <div class="mdc-notched-outline__trailing"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Clear Filters Button -->
                    <div class="filter-actions">
                        <button class="mdc-button mdc-button--outlined" id="clear-filters-btn">
                            <span class="mdc-button__ripple"></span>
                            <span class="material-icons mdc-button__icon">clear</span>
                            <span class="mdc-button__label">Clear All Filters</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flatpickr JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <!-- Material Design JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="script.js"></script>
</body>
</html>